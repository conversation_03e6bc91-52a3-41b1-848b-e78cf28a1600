[2025-07-02 21:49:59.280] [navigation_cross_region_node] [info] cross_region.cpp[2761] Adjust direction straight based on perception!

[2025-07-02 21:49:59.280] [navigation_cross_region_node] [warning] cross_region.cpp[496] [CrossRegion] [Phase 4] Straight movement duration: 3 seconds

[2025-07-02 21:49:59.300] [navigation_cross_region_node] [info] cross_region.cpp[951] [CrossRegion] [Phase 4] Channel distance: 0.35m / 0.20m

[2025-07-02 21:49:59.300] [navigation_cross_region_node] [info] cross_region.cpp[968] [CrossRegion] [Phase 4] Exceeded fixed channel distance (0.20m) without beacon detection

[2025-07-02 21:49:59.300] [navigation_cross_region_node] [info] cross_region.cpp[2236] [CrossRegion] [Nongrass2Grass1] Processing transition from non-grass to grass

[2025-07-02 21:49:59.300] [navigation_cross_region_node] [info] cross_region.cpp[1000] [CrossRegion] [Phase 4] Current area is non-grass, continue straight movement

[2025-07-02 21:49:59.300] [navigation_cross_region_node] [info] imu_data_processor.cpp[374] [ImuDataProcessor] Started continuous linear motion: velocity=0.150m/s, target_heading=0.0°

[2025-07-02 21:49:59.300] [navigation_cross_region_node] [info] imu_data_processor.cpp[625] [ImuDataProcessor] Continuous linear motion control thread started

[2025-07-02 21:49:59.300] [navigation_cross_region_node] [warning] cross_region.cpp[496] [CrossRegion] [Phase 4] Straight movement duration: 3 seconds

[2025-07-02 21:49:59.300] [navigation_cross_region_node] [info] imu_data_processor.cpp[633] [ImuDataProcessor] Continuous linear motion control thread running

[2025-07-02 21:49:59.300] [navigation_cross_region_node] [info] imu_data_processor.cpp[645] [ImuDataProcessor] Continuous motion status: target_heading=0.0°, current_heading=0.0°, heading_error=0.0°

[2025-07-02 21:49:59.312] [navigation_cross_region_node] [info] imu_data_processor.cpp[741] [ImuDataProcessor][UpdateContinuousLinearMotion] Continuous linear motion update: angular_velocity=0.043rad/s, rotation=0.053°

[2025-07-02 21:49:59.312] [navigation_cross_region_node] [info] imu_data_processor.cpp[845] [ImuDataProcessor] Position estimate: x=-0.694m, y=0.514m, yaw=-160.4°

[2025-07-02 21:49:59.321] [navigation_cross_region_node] [info] cross_region.cpp[951] [CrossRegion] [Phase 4] Channel distance: 0.35m / 0.20m

[2025-07-02 21:49:59.321] [navigation_cross_region_node] [info] cross_region.cpp[968] [CrossRegion] [Phase 4] Exceeded fixed channel distance (0.20m) without beacon detection

[2025-07-02 21:49:59.321] [navigation_cross_region_node] [info] cross_region.cpp[1000] [CrossRegion] [Phase 4] Current area is non-grass, continue straight movement

[2025-07-02 21:49:59.321] [navigation_cross_region_node] [warning] imu_data_processor.cpp[356] [ImuDataProcessor] Continuous linear motion already active

[2025-07-02 21:49:59.321] [navigation_cross_region_node] [warning] cross_region.cpp[496] [CrossRegion] [Phase 4] Straight movement duration: 3 seconds

[2025-07-02 21:49:59.324] [navigation_cross_region_node] [info] imu_data_processor.cpp[741] [ImuDataProcessor][UpdateContinuousLinearMotion] Continuous linear motion update: angular_velocity=0.008rad/s, rotation=0.062°

[2025-07-02 21:49:59.341] [navigation_cross_region_node] [info] cross_region.cpp[951] [CrossRegion] [Phase 4] Channel distance: 0.35m / 0.20m

[2025-07-02 21:49:59.341] [navigation_cross_region_node] [info] cross_region.cpp[968] [CrossRegion] [Phase 4] Exceeded fixed channel distance (0.20m) without beacon detection

[2025-07-02 21:49:59.341] [navigation_cross_region_node] [info] cross_region.cpp[1000] [CrossRegion] [Phase 4] Current area is non-grass, continue straight movement

[2025-07-02 21:49:59.341] [navigation_cross_region_node] [warning] imu_data_processor.cpp[356] [ImuDataProcessor] Continuous linear motion already active

[2025-07-02 21:49:59.341] [navigation_cross_region_node] [warning] cross_region.cpp[496] [CrossRegion] [Phase 4] Straight movement duration: 3 seconds

[2025-07-02 21:49:59.343] [navigation_cross_region_node] [info] imu_data_processor.cpp[741] [ImuDataProcessor][UpdateContinuousLinearMotion] Continuous linear motion update: angular_velocity=-0.026rad/s, rotation=0.037°

[2025-07-02 21:49:59.361] [navigation_cross_region_node] [info] cross_region.cpp[69] [CrossRegion]  Coordinate transformation MarkLocation result: detect_status: 0 mark_perception_status: 1 mark_perception_direction: 0 roi_confidence: 0 target_direction : 0 markID : 1 v_markID_dis.size : 1 xyz(-1 -1 -1) yaw(-57.29577951308232)

[2025-07-02 21:49:59.362] [navigation_cross_region_node] [info] cross_region.cpp[951] [CrossRegion] [Phase 4] Channel distance: 0.36m / 0.20m

[2025-07-02 21:49:59.362] [navigation_cross_region_node] [info] cross_region.cpp[1068] [CrossRegion] [Phase 4] Beacon detected within channel distance

[2025-07-02 21:49:59.362] [navigation_cross_region_node] [info] cross_region.cpp[1080] [CrossRegion] [Phase 4] 2.2 The mark_id_distance of the localization has a value

[2025-07-02 21:49:59.362] [navigation_cross_region_node] [info] cross_region.cpp[1089] [CrossRegion] [Phase 4] 2.2.1 The cross-region beacon is invalid

[2025-07-02 21:49:59.362] [navigation_cross_region_node] [info] cross_region.cpp[1469] [CrossRegion] Cooldown mechanism is not activated

[2025-07-02 21:49:59.362] [navigation_cross_region_node] [info] cross_region.cpp[1471] [CrossRegion] Enable perception-driven

[2025-07-02 21:49:59.367] [navigation_mower_node] [info] mower.cpp[1607] [BeaconDetection] Region exploration count: (1)

[2025-07-02 21:49:59.429] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:49:59.455] [navigation_escape_node] [error] escape.cpp[241] turn right theta:-6.8329716 ,is_loop_:false

[2025-07-02 21:49:59.645] [navigation_mower_node] [info] mower.cpp[330] [SetGrassDetecteStatus] Non-grass duration: 6 seconds

[2025-07-02 21:49:59.655] [navigation_mower_node] [info] mower.cpp[965] [NormalMowingModule1] IsStuckDetected: false, IsStuckRecoveryActive: false, ShouldPerformStuckDetection: true

[2025-07-02 21:49:59.792] [navigation_escape_node] [error] escape.cpp[63] path_long_:14.150494,theta:-6.837476

[2025-07-02 21:49:59.879] [navigation_escape_node] [error] escape_node.cpp[349] escape node result: false

[2025-07-02 21:49:59.946] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:00.033] [navigation_cross_region_node] [info] cross_region.cpp[78] [CrossRegion] mark_id_distance beacon and distance: mark_id(2), distance(0.5259823)

[2025-07-02 21:50:00.204] [navigation_mower_node] [info] stuck_detection_recovery.cpp[629] [StuckDetectionRecovery] All windows have insufficient data, skip stuck detection

[2025-07-02 21:50:00.455] [navigation_mower_node] [info] mower.cpp[260] [MowerAlg] [IsWheelSlipping1] act_linear(4.2123918e-07), act_angular(-3.1178404e-06), is_motion(true), is_slipping(false), slip_counter(0)

[2025-07-02 21:50:00.455] [navigation_escape_node] [error] escape.cpp[241] turn right theta:-6.831701 ,is_loop_:false

[2025-07-02 21:50:00.464] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:00.595] [navigation_mower_node] [info] mower.cpp[103] [MowerAlg] [SlipDetectionThread] current_slip(false)

[2025-07-02 21:50:00.595] [navigation_mower_node] [info] mower.cpp[129] [MowerAlg] [SlipDetectionThread] slip not detected!

[2025-07-02 21:50:00.670] [navigation_mower_node] [info] mower.cpp[965] [NormalMowingModule1] IsStuckDetected: false, IsStuckRecoveryActive: false, ShouldPerformStuckDetection: true

[2025-07-02 21:50:00.797] [navigation_escape_node] [error] escape.cpp[63] path_long_:14.160581,theta:-6.830647

[2025-07-02 21:50:00.877] [navigation_mower_node] [info] mower.cpp[1607] [BeaconDetection] Region exploration count: (1)

[2025-07-02 21:50:00.882] [navigation_escape_node] [error] escape_node.cpp[349] escape node result: false

[2025-07-02 21:50:00.981] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:01.023] [navigation_mower_node] [info] mower.cpp[2727] [FeatureSelection1] FeatureSelection: Cross region: includes edge following, escape

[2025-07-02 21:50:01.208] [navigation_mower_node] [info] stuck_detection_recovery.cpp[629] [StuckDetectionRecovery] All windows have insufficient data, skip stuck detection

[2025-07-02 21:50:01.231] [navigation_mower_node] [info] mower_node.cpp[1101] fusion_pose: sec: 9187, x: 1.866724, y: 1.1435348, yaw: 1.2079262, pitch: -0.026530566, roll: -0.00301406

[2025-07-02 21:50:01.251] [navigation_mower_node] [info] mower.cpp[3078] [BeaconDetection] MCU recharge request: mcu_triggers_recharge_(0)

[2025-07-02 21:50:01.272] [navigation_mower_node] [info] mower.cpp[3079] [BeaconDetection] MCU cross-region request: mcu_triggers_cross_region_(1)

[2025-07-02 21:50:01.272] [navigation_mower_node] [info] mower.cpp[3080] [BeaconDetection] MCU (random) mowing request: mcu_triggers_mower_(0)

[2025-07-02 21:50:01.272] [navigation_mower_node] [info] mower.cpp[3081] [BeaconDetection] MCU spiral mowing request: mcu_triggers_spiral_mower_(0)

[2025-07-02 21:50:01.272] [navigation_mower_node] [info] mower.cpp[3082] [BeaconDetection] MCU exploration request: mcu_triggers_region_exploration_(0)

[2025-07-02 21:50:01.272] [navigation_mower_node] [info] mower.cpp[3083] [BeaconDetection] MCU cut border request: mcu_triggers_cut_border_(0)

[2025-07-02 21:50:01.461] [navigation_escape_node] [error] escape.cpp[241] turn right theta:-6.8323765 ,is_loop_:false

[2025-07-02 21:50:01.501] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:01.687] [navigation_mower_node] [info] mower.cpp[965] [NormalMowingModule1] IsStuckDetected: false, IsStuckRecoveryActive: false, ShouldPerformStuckDetection: true

[2025-07-02 21:50:01.801] [navigation_escape_node] [error] escape.cpp[63] path_long_:14.160581,theta:-6.8336897

[2025-07-02 21:50:01.885] [navigation_escape_node] [error] escape_node.cpp[349] escape node result: false

[2025-07-02 21:50:02.016] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:02.213] [navigation_mower_node] [info] stuck_detection_recovery.cpp[629] [StuckDetectionRecovery] All windows have insufficient data, skip stuck detection

[2025-07-02 21:50:02.222] [navigation_mower_node] [warning] mower_node.cpp[1038] [MowerThread] MCUException data timeout 3596384 ms, status restored to NORMAL

[2025-07-02 21:50:02.222] [navigation_mower_node] [warning] mower_node.cpp[1050] [MowerThread] behavior_state_ data timeout over 3686921 ms, status reset to UNDEFINED

[2025-07-02 21:50:02.389] [navigation_mower_node] [info] mower.cpp[1607] [BeaconDetection] Region exploration count: (1)

[2025-07-02 21:50:02.463] [navigation_escape_node] [error] escape.cpp[241] turn right theta:-6.8342824 ,is_loop_:false

[2025-07-02 21:50:02.490] [navigation_mower_node] [info] stuck_detection_recovery.cpp[487] [StuckDetectionRecovery] Received motor data: left=-0.0RPM, right=-0.0RPM

[2025-07-02 21:50:02.536] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:02.702] [navigation_mower_node] [info] mower.cpp[965] [NormalMowingModule1] IsStuckDetected: false, IsStuckRecoveryActive: false, ShouldPerformStuckDetection: true

[2025-07-02 21:50:02.803] [navigation_escape_node] [error] escape.cpp[63] path_long_:14.160581,theta:-6.8356724

[2025-07-02 21:50:02.887] [navigation_escape_node] [error] escape_node.cpp[349] escape node result: false

[2025-07-02 21:50:03.037] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:03.037] [navigation_mower_node] [info] mower.cpp[2727] [FeatureSelection1] FeatureSelection: Cross region: includes edge following, escape

[2025-07-02 21:50:03.218] [navigation_mower_node] [info] stuck_detection_recovery.cpp[629] [StuckDetectionRecovery] All windows have insufficient data, skip stuck detection

[2025-07-02 21:50:03.249] [navigation_mower_node] [info] mower_node.cpp[1101] fusion_pose: sec: 9189, x: 1.8668375, y: 1.1438347, yaw: 1.2107915, pitch: -0.027141033, roll: -0.0018635864

[2025-07-02 21:50:03.269] [navigation_mower_node] [info] mower.cpp[3078] [BeaconDetection] MCU recharge request: mcu_triggers_recharge_(0)

[2025-07-02 21:50:03.290] [navigation_mower_node] [info] mower.cpp[3079] [BeaconDetection] MCU cross-region request: mcu_triggers_cross_region_(1)

[2025-07-02 21:50:03.291] [navigation_mower_node] [info] mower.cpp[3080] [BeaconDetection] MCU (random) mowing request: mcu_triggers_mower_(0)

[2025-07-02 21:50:03.291] [navigation_mower_node] [info] mower.cpp[3081] [BeaconDetection] MCU spiral mowing request: mcu_triggers_spiral_mower_(0)

[2025-07-02 21:50:03.291] [navigation_mower_node] [info] mower.cpp[3082] [BeaconDetection] MCU exploration request: mcu_triggers_region_exploration_(0)

[2025-07-02 21:50:03.291] [navigation_mower_node] [info] mower.cpp[3083] [BeaconDetection] MCU cut border request: mcu_triggers_cut_border_(0)

[2025-07-02 21:50:03.469] [navigation_escape_node] [error] escape.cpp[241] turn right theta:-6.8372617 ,is_loop_:false

[2025-07-02 21:50:03.558] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:03.723] [navigation_mower_node] [info] mower.cpp[965] [NormalMowingModule1] IsStuckDetected: false, IsStuckRecoveryActive: false, ShouldPerformStuckDetection: true

[2025-07-02 21:50:03.807] [navigation_escape_node] [error] escape.cpp[63] path_long_:14.160581,theta:-6.8382726

[2025-07-02 21:50:03.892] [navigation_escape_node] [error] escape_node.cpp[349] escape node result: false

[2025-07-02 21:50:03.908] [navigation_mower_node] [info] mower.cpp[1607] [BeaconDetection] Region exploration count: (1)

[2025-07-02 21:50:04.074] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:04.222] [navigation_mower_node] [info] stuck_detection_recovery.cpp[629] [StuckDetectionRecovery] All windows have insufficient data, skip stuck detection

[2025-07-02 21:50:04.392] [navigation_cross_region_node] [info] cross_region.cpp[69] [CrossRegion]  Coordinate transformation MarkLocation result: detect_status: 0 mark_perception_status: 1 mark_perception_direction: 0 roi_confidence: 0 target_direction : 0 markID : 1 v_markID_dis.size : 1 xyz(-1 -1 -1) yaw(-57.29577951308232)

[2025-07-02 21:50:04.470] [navigation_escape_node] [error] escape.cpp[241] turn right theta:-6.8389254 ,is_loop_:false

[2025-07-02 21:50:04.591] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:04.735] [navigation_mower_node] [info] mower.cpp[965] [NormalMowingModule1] IsStuckDetected: false, IsStuckRecoveryActive: false, ShouldPerformStuckDetection: true

[2025-07-02 21:50:04.810] [navigation_escape_node] [error] escape.cpp[63] path_long_:14.160581,theta:-6.8392844

[2025-07-02 21:50:04.894] [navigation_escape_node] [error] escape_node.cpp[349] escape node result: false

[2025-07-02 21:50:05.044] [navigation_mower_node] [info] mower.cpp[2727] [FeatureSelection1] FeatureSelection: Cross region: includes edge following, escape

[2025-07-02 21:50:05.072] [navigation_cross_region_node] [info] cross_region.cpp[78] [CrossRegion] mark_id_distance beacon and distance: mark_id(2), distance(0.53336626)

[2025-07-02 21:50:05.105] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:05.227] [navigation_mower_node] [info] stuck_detection_recovery.cpp[629] [StuckDetectionRecovery] All windows have insufficient data, skip stuck detection

[2025-07-02 21:50:05.228] [navigation_mower_node] [warning] mower_node.cpp[1038] [MowerThread] MCUException data timeout 3599390 ms, status restored to NORMAL

[2025-07-02 21:50:05.228] [navigation_mower_node] [warning] mower_node.cpp[1050] [MowerThread] behavior_state_ data timeout over 3689927 ms, status reset to UNDEFINED

[2025-07-02 21:50:05.269] [navigation_mower_node] [info] mower.cpp[3078] [BeaconDetection] MCU recharge request: mcu_triggers_recharge_(0)

[2025-07-02 21:50:05.270] [navigation_mower_node] [info] mower_node.cpp[1101] fusion_pose: sec: 9191, x: 1.8669508, y: 1.1441369, yaw: 1.2132586, pitch: -0.02872809, roll: -0.0007107955

[2025-07-02 21:50:05.311] [navigation_mower_node] [info] mower.cpp[3079] [BeaconDetection] MCU cross-region request: mcu_triggers_cross_region_(1)

[2025-07-02 21:50:05.311] [navigation_mower_node] [info] mower.cpp[3080] [BeaconDetection] MCU (random) mowing request: mcu_triggers_mower_(0)

[2025-07-02 21:50:05.311] [navigation_mower_node] [info] mower.cpp[3081] [BeaconDetection] MCU spiral mowing request: mcu_triggers_spiral_mower_(0)

[2025-07-02 21:50:05.311] [navigation_mower_node] [info] mower.cpp[3082] [BeaconDetection] MCU exploration request: mcu_triggers_region_exploration_(0)

[2025-07-02 21:50:05.311] [navigation_mower_node] [info] mower.cpp[3083] [BeaconDetection] MCU cut border request: mcu_triggers_cut_border_(0)

[2025-07-02 21:50:05.414] [navigation_mower_node] [info] mower.cpp[1607] [BeaconDetection] Region exploration count: (1)

[2025-07-02 21:50:05.466] [navigation_mower_node] [info] mower.cpp[260] [MowerAlg] [IsWheelSlipping1] act_linear(-0), act_angular(0), is_motion(false), is_slipping(false), slip_counter(0)

[2025-07-02 21:50:05.471] [navigation_escape_node] [error] escape.cpp[241] turn right theta:-6.8386364 ,is_loop_:false

[2025-07-02 21:50:05.608] [navigation_mower_node] [info] mower.cpp[103] [MowerAlg] [SlipDetectionThread] current_slip(false)

[2025-07-02 21:50:05.608] [navigation_mower_node] [info] mower.cpp[129] [MowerAlg] [SlipDetectionThread] slip not detected!

[2025-07-02 21:50:05.622] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:05.747] [navigation_mower_node] [info] mower.cpp[965] [NormalMowingModule1] IsStuckDetected: false, IsStuckRecoveryActive: false, ShouldPerformStuckDetection: true

[2025-07-02 21:50:05.815] [navigation_escape_node] [error] escape.cpp[63] path_long_:14.160581,theta:-6.8388014

[2025-07-02 21:50:05.894] [navigation_escape_node] [error] escape_node.cpp[349] escape node result: false

[2025-07-02 21:50:06.139] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:06.230] [navigation_mower_node] [info] stuck_detection_recovery.cpp[629] [StuckDetectionRecovery] All windows have insufficient data, skip stuck detection

[2025-07-02 21:50:06.475] [navigation_escape_node] [error] escape.cpp[241] turn right theta:-6.840278 ,is_loop_:false

[2025-07-02 21:50:06.641] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:06.765] [navigation_mower_node] [info] mower.cpp[965] [NormalMowingModule1] IsStuckDetected: false, IsStuckRecoveryActive: false, ShouldPerformStuckDetection: true

[2025-07-02 21:50:06.819] [navigation_escape_node] [error] escape.cpp[63] path_long_:14.160581,theta:-6.841353

[2025-07-02 21:50:06.898] [navigation_escape_node] [error] escape_node.cpp[349] escape node result: false

[2025-07-02 21:50:06.931] [navigation_mower_node] [info] mower.cpp[1607] [BeaconDetection] Region exploration count: (1)

[2025-07-02 21:50:07.057] [navigation_mower_node] [info] mower.cpp[2727] [FeatureSelection1] FeatureSelection: Cross region: includes edge following, escape

[2025-07-02 21:50:07.161] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:07.235] [navigation_mower_node] [info] stuck_detection_recovery.cpp[629] [StuckDetectionRecovery] All windows have insufficient data, skip stuck detection

[2025-07-02 21:50:07.284] [navigation_mower_node] [info] mower.cpp[3078] [BeaconDetection] MCU recharge request: mcu_triggers_recharge_(0)

[2025-07-02 21:50:07.285] [navigation_mower_node] [info] mower_node.cpp[1101] fusion_pose: sec: 9193, x: 1.8670663, y: 1.1444471, yaw: 1.2158216, pitch: -0.029163709, roll: -0.00042271477

[2025-07-02 21:50:07.327] [navigation_mower_node] [info] mower.cpp[3079] [BeaconDetection] MCU cross-region request: mcu_triggers_cross_region_(1)

[2025-07-02 21:50:07.327] [navigation_mower_node] [info] mower.cpp[3080] [BeaconDetection] MCU (random) mowing request: mcu_triggers_mower_(0)

[2025-07-02 21:50:07.327] [navigation_mower_node] [info] mower.cpp[3081] [BeaconDetection] MCU spiral mowing request: mcu_triggers_spiral_mower_(0)

[2025-07-02 21:50:07.327] [navigation_mower_node] [info] mower.cpp[3082] [BeaconDetection] MCU exploration request: mcu_triggers_region_exploration_(0)

[2025-07-02 21:50:07.327] [navigation_mower_node] [info] mower.cpp[3083] [BeaconDetection] MCU cut border request: mcu_triggers_cut_border_(0)

[2025-07-02 21:50:07.475] [navigation_escape_node] [error] escape.cpp[241] turn right theta:-6.842971 ,is_loop_:false

[2025-07-02 21:50:07.588] [navigation_mower_node] [info] stuck_detection_recovery.cpp[487] [StuckDetectionRecovery] Received motor data: left=-0.0RPM, right=-0.0RPM

[2025-07-02 21:50:07.680] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:07.785] [navigation_mower_node] [info] mower.cpp[965] [NormalMowingModule1] IsStuckDetected: false, IsStuckRecoveryActive: false, ShouldPerformStuckDetection: true

[2025-07-02 21:50:07.824] [navigation_escape_node] [error] escape.cpp[63] path_long_:14.160581,theta:-6.845048

[2025-07-02 21:50:07.898] [navigation_escape_node] [error] escape_node.cpp[349] escape node result: false

[2025-07-02 21:50:08.182] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:08.241] [navigation_mower_node] [info] stuck_detection_recovery.cpp[629] [StuckDetectionRecovery] All windows have insufficient data, skip stuck detection

[2025-07-02 21:50:08.244] [navigation_mower_node] [warning] mower_node.cpp[1038] [MowerThread] MCUException data timeout 3602406 ms, status restored to NORMAL

[2025-07-02 21:50:08.244] [navigation_mower_node] [warning] mower_node.cpp[1050] [MowerThread] behavior_state_ data timeout over 3692943 ms, status reset to UNDEFINED

[2025-07-02 21:50:08.451] [navigation_mower_node] [info] mower.cpp[1607] [BeaconDetection] Region exploration count: (1)

[2025-07-02 21:50:08.476] [navigation_escape_node] [error] escape.cpp[241] turn right theta:-6.8460073 ,is_loop_:false

[2025-07-02 21:50:08.699] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:08.802] [navigation_mower_node] [info] mower.cpp[965] [NormalMowingModule1] IsStuckDetected: false, IsStuckRecoveryActive: false, ShouldPerformStuckDetection: true

[2025-07-02 21:50:08.829] [navigation_escape_node] [error] escape.cpp[63] path_long_:14.160581,theta:-6.8462944

[2025-07-02 21:50:08.902] [navigation_escape_node] [error] escape_node.cpp[349] escape node result: false

[2025-07-02 21:50:09.070] [navigation_mower_node] [info] mower.cpp[2727] [FeatureSelection1] FeatureSelection: Cross region: includes edge following, escape

[2025-07-02 21:50:09.213] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:09.246] [navigation_mower_node] [info] stuck_detection_recovery.cpp[629] [StuckDetectionRecovery] All windows have insufficient data, skip stuck detection

[2025-07-02 21:50:09.295] [navigation_mower_node] [info] mower.cpp[3078] [BeaconDetection] MCU recharge request: mcu_triggers_recharge_(0)

[2025-07-02 21:50:09.295] [navigation_mower_node] [info] mower_node.cpp[1101] fusion_pose: sec: 9195, x: 1.8671846, y: 1.1447679, yaw: 1.2182317, pitch: -0.029486915, roll: 0.00032455576

[2025-07-02 21:50:09.336] [navigation_mower_node] [info] mower.cpp[3079] [BeaconDetection] MCU cross-region request: mcu_triggers_cross_region_(1)

[2025-07-02 21:50:09.336] [navigation_mower_node] [info] mower.cpp[3080] [BeaconDetection] MCU (random) mowing request: mcu_triggers_mower_(0)

[2025-07-02 21:50:09.336] [navigation_mower_node] [info] mower.cpp[3081] [BeaconDetection] MCU spiral mowing request: mcu_triggers_spiral_mower_(0)

[2025-07-02 21:50:09.336] [navigation_mower_node] [info] mower.cpp[3082] [BeaconDetection] MCU exploration request: mcu_triggers_region_exploration_(0)

[2025-07-02 21:50:09.336] [navigation_mower_node] [info] mower.cpp[3083] [BeaconDetection] MCU cut border request: mcu_triggers_cut_border_(0)

[2025-07-02 21:50:09.451] [navigation_cross_region_node] [info] cross_region.cpp[69] [CrossRegion]  Coordinate transformation MarkLocation result: detect_status: 0 mark_perception_status: 1 mark_perception_direction: 0 roi_confidence: 0 target_direction : 0 markID : 1 v_markID_dis.size : 1 xyz(-1 -1 -1) yaw(-57.29577951308232)

[2025-07-02 21:50:09.481] [navigation_escape_node] [error] escape.cpp[241] turn right theta:-6.847829 ,is_loop_:false

[2025-07-02 21:50:09.731] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:09.814] [navigation_mower_node] [info] mower.cpp[965] [NormalMowingModule1] IsStuckDetected: false, IsStuckRecoveryActive: false, ShouldPerformStuckDetection: true

[2025-07-02 21:50:09.834] [navigation_escape_node] [error] escape.cpp[63] path_long_:14.160581,theta:-6.8477516

[2025-07-02 21:50:09.903] [navigation_escape_node] [error] escape_node.cpp[349] escape node result: false

[2025-07-02 21:50:09.959] [navigation_mower_node] [info] mower.cpp[1607] [BeaconDetection] Region exploration count: (1)

[2025-07-02 21:50:10.142] [navigation_cross_region_node] [info] cross_region.cpp[78] [CrossRegion] mark_id_distance beacon and distance: mark_id(2), distance(0.5323021)

[2025-07-02 21:50:10.248] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode

[2025-07-02 21:50:10.255] [navigation_mower_node] [info] stuck_detection_recovery.cpp[629] [StuckDetectionRecovery] All windows have insufficient data, skip stuck detection

[2025-07-02 21:50:10.482] [navigation_escape_node] [error] escape.cpp[241] turn right theta:-6.8484917 ,is_loop_:false

[2025-07-02 21:50:10.483] [navigation_mower_node] [info] mower.cpp[260] [MowerAlg] [IsWheelSlipping1] act_linear(-0), act_angular(0), is_motion(false), is_slipping(false), slip_counter(0)

[2025-07-02 21:50:10.624] [navigation_mower_node] [info] mower.cpp[103] [MowerAlg] [SlipDetectionThread] current_slip(false)

[2025-07-02 21:50:10.624] [navigation_mower_node] [info] mower.cpp[129] [MowerAlg] [SlipDetectionThread] slip not detected!

[2025-07-02 21:50:10.768] [navigation_mower_node] [info] mower.cpp[2018] [BeaconDetection] Start cross-region mode